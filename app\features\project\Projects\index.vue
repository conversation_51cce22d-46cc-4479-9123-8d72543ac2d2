<template>
  <div>
    <TeleportSafe
      to="#page-header"
    >
      <Button

        trailing-icon="basil:plus-solid"
        variant="solid"
        :to="routes.project.projectCreate.to"
      >
        สร้างโครงการใหม่
      </Button>
    </TeleportSafe>
    <FormFields
      :form="form"
      :options="formFields"
      class="mb-4"
    />

    <Table
      :options="tableOptions"
      @search="project.fetchSearch"
    >
      <template #actions-cell="{ row }">
        <Button
          icon="material-symbols:edit-square-outline-rounded"
          class="text-[#757575]"
          variant="ghost"
          color="neutral"
          square
          :to="routes.project.projectById(row.original.id).to"
        />
      </template>
    </Table>
  </div>
</template>

<script lang="ts" setup>
import { COLUMN_TYPES } from '#core/components/Table/types'
import { watchDebounced } from '@vueuse/core'
import DateTime from '~/components/Column/DateTime.vue'
import { routes } from '~/constants/routes'

const project = useProjectPageLoader()

const route = useRoute()

const form = useForm({
  validationSchema: toTypedSchema(v.object({
    q: v.optional(v.pipe(v.string()), ''),
  })),
})

const formFields = createFormFields(() => [
  {
    type: INPUT_TYPES.SEARCH,
    props: {
      name: 'q',
      placeholder: 'ค้นหาชื่อผู้ใช้งาน',
    },
  },
])

watchDebounced(form.values, (values) => {
  project.fetchSearch(values.q || '')
}, {
  debounce: 300,
  deep: true,
})

const tableOptions = useTable<IProject>({
  options: {
    isRouteChange: true,
  },
  repo: project,
  columns: () => [
    {
      accessorKey: 'name',
      header: 'โครงการ',
      type: COLUMN_TYPES.TEXT,
    },
    {
      accessorKey: 'contact_name',
      header: 'ชื่อผู้ติดต่อ',
      type: COLUMN_TYPES.TEXT,
    },
    {
      accessorKey: 'contact_email',
      header: 'อีเมลผู้ติดต่อ',
    },

    {
      accessorKey: 'active_at',
      header: 'วันที่เริ่มใช้งาน',
      type: COLUMN_TYPES.COMPONENT,
      component: DateTime,
    },
    {
      accessorKey: 'status',
      header: 'สถานะ',
      meta: {
        class: {
          th: 'text-center',
        },
      },
    },
    {
      accessorKey: 'actions',
      header: '',
    },
  ],
})

project.fetchSetLoading()
onMounted(() => {
  project.fetchPage(Number(route.query.page || 1), route.query.q as string, {
    params: route.query,
  })
})
</script>
