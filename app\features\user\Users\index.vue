<template>
  <div>
    <TeleportSafe to="#page-header">
      <Button
        trailing-icon="basil:plus-solid"
        :to="routes.user.userCreate.to"
      >
        เพิ่มผู้ใช้งาน
      </Button>
    </TeleportSafe>
    <FormFields
      :form="form"
      :options="formFields"
      class="mb-4"
    />
    <Table
      :options="tableOptions"
      @pageChange="users.fetchPageChange"
      @search="users.fetchSearch"
    >
      <template #type-cell="{ row }">
        <Badge
          :color="row.original.type === UserType.ADMIN ? 'info' : 'warning'"
          :label="row.original.type"
          variant="soft"
          class="rounded-none font-bold"
        />
      </template>
      <template #actions-cell="{ row }">
        <div class="flex justify-end">
          <Button
            icon="mage:pen"
            color="neutral"
            variant="link"
            :to="routes.user.userById(row.original.id).to"
          />
          <ButtonActionIcon
            icon="prime:trash"
            color="error"
            @click="onDelete(row.original)"
          />
        </div>
      </template>
    </Table>
  </div>
</template>

<script lang="ts" setup>
import * as v from 'valibot'
import { watchDebounced } from '@vueuse/core'
import type { IUser } from '~/types/user'
import { useUserPageLoader } from '~/loaders/user'
import { COLUMN_TYPES } from '#core/components/Table/types'
import { INPUT_TYPES } from '#core/components/Form/types'
import DateTime from '~/components/Column/DateTime.vue'

const users = useUserPageLoader()
const dialog = useDialog()
const noti = useNotification()

const form = useForm({
  validationSchema: toTypedSchema(v.object({
    q: v.optional(v.pipe(v.string()), ''),
  })),
})

const formFields = createFormFields(() => [
  {
    type: INPUT_TYPES.SEARCH,
    props: {
      name: 'q',
      placeholder: 'ค้นหาชื่อผู้ใช้งาน',
    },
  },
])

users.fetchSetLoading()
onMounted(() => {
  users.fetchPage()
})

const tableOptions = useTable<IUser>({
  repo: users,
  columns: () => [
    {
      accessorKey: 'display_name',
      header: 'ชื่อผู้ใช้งาน',
      type: COLUMN_TYPES.TEXT,
      meta: {
        max: 40,
      },
    },
    {
      accessorKey: 'email',
      header: 'อีเมล',

      type: COLUMN_TYPES.TEXT,

    },
    {
      accessorKey: 'type',
      header: 'ประเภทผู้ใช้งาน',
      type: COLUMN_TYPES.TEXT,
      meta: {
        class: {
          th: 'text-center',
          td: 'text-center',
        },
      },

    },
    {
      accessorKey: 'created_at',
      header: 'วันที่สร้าง',
      type: COLUMN_TYPES.COMPONENT,
      component: DateTime,
    },
    {
      accessorKey: 'actions',
      header: '',

    },
  ],
})

const onDelete = (values: IUser) => {
  dialog.confirm({
    title: 'ยืนยันการลบ',
    description: `คุณต้องการลบผู้ใช้งาน "${values.display_name}" หรือไม่?`,
    confirmText: 'ยืนยัน',
    cancelText: 'ยกเลิก',
  }).then(() => {
    users.deleteRun(values.id)
    dialog.loading({
      title: 'กรุณารอสักครู่...',
      description: 'กำลังส่งข้อมูล...',
    })
  })
}

watchDebounced(form.values, (values) => {
  users.fetchSearch(values.q || '')
}, {
  debounce: 300,
  deep: true,
})

useWatchTrue(
  () => users.delete.status.isSuccess,
  () => {
    noti.success({
      title: 'ลบผู้ใช้งานสำเร็จ',
      description: 'คุณได้ลบกระทรวงเรียบร้อยแล้ว',
    })
  },
)

useWatchTrue(
  () => users.delete.status.isError,
  () => {
    noti.error({
      title: 'ลบผู้ใช้งานไม่สำเร็จ',
      description: 'เกิดข้อผิดพลาดในการลบกระทรวง กรุณาลองใหม่อีกครั้ง',
    })
  },
)
</script>
