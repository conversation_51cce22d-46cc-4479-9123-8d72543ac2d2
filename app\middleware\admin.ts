import { routes } from '~/constants/routes'

export default defineNuxtRouteMiddleware(async (to, _from) => {
  const nuxtApp = useNuxtApp()

  if (import.meta.client && nuxtApp.isHydrating && nuxtApp.payload.serverRendered) {
    return
  }

  const auth = useAuth()

  if (!auth.isAuthenticated.value) {
    return navigateTo({
      path: routes.login.to,
      query: {
        redirect: to.fullPath,
      },
    })
  }

  await auth.fetchMe.run()

  if (auth.fetchMe.status.value.isError) {
    return navigateTo({
      path: routes.logout.to,
      query: {
        redirect: to.fullPath,
      },
    }, {
      external: true,
    })
  }
})
