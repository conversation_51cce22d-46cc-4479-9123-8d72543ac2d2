<template>
  <Modal
    :close="{ onClick: () => emits('close', false) }"
    :dismissible="false"
    :title="isEditing ? 'แก้ไขกรม' : 'เพิ่มกรม'"
  >
    <template #body>
      <form @submit="onSubmit">
        <FormFields :options="formFields" />
        <div class="mt-4 flex justify-end gap-3">
          <Button
            variant="outline"
            color="neutral"
            @click="emits('close', false)"
          >
            ยกเลิก
          </Button>
          <Button
            :loading="status().isLoading"
            :disabled="!form.meta.value.dirty"
            type="submit"
          >
            {{ isEditing ? 'บันทึก' : 'เพิ่ม' }}
          </Button>
        </div>
      </form>
    </template>
  </Modal>
</template>

<script lang="ts" setup>
import * as v from 'valibot'
import { INPUT_TYPES } from '#core/components/Form/types'
import { useOrgMinistryPageLoader } from '~/loaders/organizations'
import type { IDepartment } from '~/types/organizations'
import type { IStatus } from '#core/types/lib'
import { <PERSON>a<PERSON><PERSON><PERSON> } from '~/helpers/MaskaHelper'

const emits = defineEmits<{ close: [boolean] }>()

const props = defineProps<{
  isEditing?: boolean
  values?: IDepartment | null
  status: () => IStatus
  onSubmit: (values: IDepartment) => void
}>()

const ministry = useOrgMinistryPageLoader()
const form = useForm({
  initialValues: props.values,
  validationSchema: toTypedSchema(v.object({
    name_th: v.optional(v.pipe(v.string(), v.nonEmpty()), ''),
    name_en: v.optional(v.pipe(v.string(), v.nonEmpty()), ''),
    ministry_id: v.optional(v.pipe(v.string(), v.nonEmpty()), ''),
  })),
})

ministry.fetchSetLoading()
onMounted(() => {
  ministry.fetchPage(1, '', {
    params: {
      limit: 10000,
    },
  })
})

const formFields = createFormFields(() => [
  {
    type: INPUT_TYPES.SELECT,
    props: {
      label: 'กระทรวง',
      name: 'ministry_id',
      placeholder: 'กรุณาเลือกกระทรวง',
      required: true,
      options: ArrayHelper.toOptions(ministry.fetch.items, 'id', 'name_th'),
      loading: ministry.fetch.status.isLoading,
    },
  },
  {
    type: INPUT_TYPES.TEXT,
    props: {
      label: 'ชื่อกรม (ภาษาไทย)',
      name: 'name_th',
      placeholder: 'กรุณากรอกชื่อกรม (ภาษาไทย)',
      required: true,
      ...MaskaHelper.thaiLetter(),
    },
  },
  {
    type: INPUT_TYPES.TEXT,
    props: {
      label: 'ชื่อกรม (ภาษาอังกฤษ)',
      name: 'name_en',
      placeholder: 'กรุณากรอกชื่อกรม (ภาษาอังกฤษ)',
      required: true,
      ...MaskaHelper.englishLetter(),
    },
  },
])

const onSubmit = form.handleSubmit((values) => {
  props.onSubmit(values as IDepartment)
})
</script>
