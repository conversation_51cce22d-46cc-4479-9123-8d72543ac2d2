<template>
  <NuxtLayout>
    <Divisions />
  </NuxtLayout>
</template>

<script lang="ts" setup>
import { routes } from '~/constants/routes'
import Divisions from '~/features/organizations/Divisions/index.vue'

definePageMeta({
  middleware: ['admin'],
  layout: 'admin',
})

useSeoMeta({
  title: routes.organization.divisions.label,
})

useApp().definePage({
  title: routes.organization.divisions.label,
  breadcrumbs: [routes.organization.divisions],
})
</script>
