export default defineEventHandler(async (event) => {
  setCookie(event, 'authorization', '', {
    maxAge: -1, // Expire the cookie immediately
    path: '/',
  })

  setCookie(event, 'user_role_context', '', {
    maxAge: -1, // Expire the cookie immediately
    path: '/',
  })

  // Get redirect parameter from query
  const query = getQuery(event)
  const redirectTo = query.redirect as string

  // Construct login URL with redirect parameter
  const loginUrl = redirectTo
    ? `/login?redirect=${encodeURIComponent(redirectTo)}`
    : '/login'

  return sendRedirect(event, loginUrl, 302)
})
