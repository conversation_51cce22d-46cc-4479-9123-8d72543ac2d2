export interface IUser extends Record<string, any> {
  id: string
  position: any
  company_address: any
  prefix: any
  firstname: any
  lastname: any
  prefix_en: any
  firstname_en: any
  lastname_en: any
  phone: any
  work_phone: any
  email: string
  id_card: any
  date_of_birth: any
  is_registered: boolean
  is_genarate: boolean
  is_change_password: boolean
  last_login: string
  username: string
  password: string
  company_name: any
  company_position: any
  temp_password: any
  user_type: UserRole
  organization_type: any
  status: string
  mail_group: any
  file_name: any
  file_url: any
  role: 'user' | 'admin'
  accessToken: string
  refreshToken: string
  idp_user_id: string
  idp_roles_value: any
  idp_id_token: string
  idp_access_token: string
  idp_refresh_token: string
  idp_scope: string
  idp_token_type: string
  idp_expires_in: number
  idp_user_sub: string
  idp_user_groups: any
  idp_user_pid: any
  idp_user_given_name: string
  idp_user_family_name: string
  idp_user_email: string
  idp_user_username: string
  idp_user_password: any
  fa2_secret: any
  ministry_id: any
  department_id: any
  division_id: any
  is_accpt_tou: boolean
  is_accpt_pdpa: boolean
  created_at: string
  updated_at: string
  delete_at: any
  create_by: any
  update_by: any
  delete_by: any
  province_id: any
  district_id: any
  sub_district_id: any
  ministry: any
  department: any
  division: any
  province: any
  district: any
  sub_district: any
  userStatuses: any[]
  isMultiRole: boolean
}

export interface UserLogs {
  id: string
  action: string
  type: string
  create_by: string | null
  update_by: string | null
  created_at: string
  updated_at: string
  mail_status: boolean
  mail_to: string
  target_id: string
  user: IUser
  user_id: string
}

export enum UserRoleContext {
  PSA = 'psa',
  TENANT = 'tenant',
}

export enum UserRole {
  TENANT = 'tenant',
  PSA = 'project_scope',
}
