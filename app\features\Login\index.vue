<template>
  <div class="grid min-h-screen w-full place-items-center">
    <Card>
      <div
        class="relative grid h-full place-items-center px-4"
      >
        <div class="w-full max-w-[410px]">
          <div class="mb-4 text-center text-4xl font-bold">
            Cloud Sale Portal
          </div>
          <h1 class="text-center text-3xl font-bold">
            เข้าสู่ระบบ
          </h1>

          <Form
            class="mt-8 w-full"
            @submit="onSubmit"
          >
            <Alert
              v-if="auth.login.status.value.isError || auth.fetchMe.status.value.isError"
              title="การเข้าสู่ระบบล้มเหลว"
              description="โปรดตรวจสอบชื่อผู้ใช้และรหัสผ่านของคุณแล้วลองอีกครั้ง"
              color="error"
              class="mb-4 text-center"
              variant="soft"
            />
            <FormFields
              class="w-[410px]"
              :options="fields"
            />

            <div class="w-full">
              <Button
                block
                type="submit"
                :loading="auth.login.status.value.isLoading || auth.fetchMe.status.value.isLoading"
                class="mt-6 justify-center"
              >
                เข้าสู่ระบบ
              </Button>
            </div>
          </Form>
        </div>
      </div>
    </Card>
  </div>
</template>

<script lang="ts" setup>
const auth = useAuth()
const router = useRouter()
const form = useForm({
  validationSchema: toTypedSchema(
    v.object({
      email: v.optional(v.pipe(v.string(), v.nonEmpty()), ''),
      password: v.optional(v.pipe(v.string(), v.nonEmpty()), ''),
    }),
  ),
})

const fields = createFormFields(() => [
  {
    type: INPUT_TYPES.TEXT,
    props: {
      label: 'ชื่อผู้ใช้งาน',
      name: 'email',
    },
  },
  {
    type: INPUT_TYPES.PASSWORD,
    props: {
      label: 'รหัสผ่าน',
      name: 'password',
    },
  },
])

const onSubmit = form.handleSubmit((values) => {
  auth.login.run({
    data: {
      email: values.email,
      password: values.password.trim(),
    },
  })
})

useWatchTrue(() => auth.login.status.value.isSuccess, () => {
  auth.fetchMe.run({
    data: {
      token: auth.login.data.value!.token,
    },
  })
})

useWatchTrue(() => auth.fetchMe.status.value.isSuccess, () => {
  auth.token.value = auth.login.data.value!.token

  router.push(routes.home.to)
})
</script>
