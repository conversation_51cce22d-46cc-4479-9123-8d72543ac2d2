<template>
  <Form
    @submit="onSubmit"
  >
    <Card>
      <div class="mb-5 text-lg font-bold">
        ข้อมูลผู้ใช้งาน
      </div>

      <FormFields
        class="mb-4 grid gap-3 md:grid-cols-2"
        :options="formProjectCode"
      />
      <FormFields
        class="grid gap-3"
        :options="formNameProject"
      />
    </Card>
    <div
      class="mt-2 flex w-full justify-end"
    >
      <Button
        type="submit"
        class="mt-6"
        icon="material-symbols:check"
      >
        บันทึก
      </Button>
    </div>
  </Form>
</template>

<script lang="ts" setup>
import Password from '../Password.vue'

const noti = useNotification()
const dialog = useDialog()
const users = useUserPageLoader()
const router = useRouter()
const isPassword = ref('')
const overlay = useOverlay()
const passwordModal = overlay.create(Password)
const form = useForm({
  validationSchema: toTypedSchema(v.object({
    display_name: v.optional(v.pipe(v.string(), v.nonEmpty('กรุณากรอกชื่อผู้ใช้งาน')), ''),
    email: v.optional(v.pipe(v.string(), v.nonEmpty('กรุณากรอกอีเมล')), ''),
    type: v.optional(v.pipe(v.string(), v.nonEmpty('กรุณาเลือกประเภทผู้ใช้งาน')), ''),
  })),
  keepValuesOnUnmount: true,
})

const formProjectCode = createFormFields(() => [
  {
    type: INPUT_TYPES.TEXT,
    props: {
      name: 'display_name',
      label: 'ชื่อในการแสดงผล',
      required: true,
      autoFocus: true,
    },
  },
  {
    type: INPUT_TYPES.TEXT,
    props: {
      name: 'email',
      label: 'อีเมล',
      required: true,
    },
  },
])

const formNameProject = createFormFields(() => [
  {
    type: INPUT_TYPES.RADIO,
    props: {
      name: 'type',
      label: 'ประเภทผู้ใช้งาน',
      required: true,
      options: [
        {
          value: UserType.ADMIN,
          label: 'Admin',
        },
        {
          value: UserType.USER,
          label: 'User',
        },
      ],
    },
  },

])

const generatePassword = (length = 10): string => {
  const upperChars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ'
  const lowerChars = 'abcdefghijklmnopqrstuvwxyz'
  const numberChars = '0123456789'
  const specialChars = '!@#$%^&*()_+-=[]{}|;:,.<>?'

  const upper = upperChars.charAt(Math.floor(Math.random() * upperChars.length))
  const lower = lowerChars.charAt(Math.floor(Math.random() * lowerChars.length))
  const number = numberChars.charAt(Math.floor(Math.random() * numberChars.length))
  const special = specialChars.charAt(Math.floor(Math.random() * specialChars.length))

  const allChars = upperChars + lowerChars + numberChars + specialChars

  const remainingLength = length - 4
  let password = upper + lower + number + special

  for (let i = 0; i < remainingLength; i++) {
    password += allChars[Math.floor(Math.random() * allChars.length)]
  }

  return password
    .split('')
    .sort(() => Math.random() - 0.5)
    .join('')
}

const onSubmit = form.handleSubmit((values) => {
  dialog.confirm({
    title: `ยืนยันสร้างผู้ใช้งานใช่หรือไม่`,
  }).then(() => {
    isPassword.value = generatePassword()

    users.addRun({
      data: {
        ...values,
        password: isPassword.value,
      },
    })

    dialog.loading({
      title: 'กรุณารอสักครู่...',
      description: 'กำลังส่งข้อมูล...',
    })
  })
}, moveToError)

useWatchTrue(
  () => users.add.status.isSuccess,
  () => {
    dialog.close()
    noti.success({
      title: 'สร้างผู้ใช้งานสำเร็จ',

    })

    passwordModal.open({
      id: isPassword.value,
      onClose: () => {
        passwordModal.close()
        router.push(routes.user.userById(users.add.item?.id || '1').to)
      },
    })
  },
)

useWatchTrue(
  () => users.add.status.isError,
  () => {
    noti.error({
      title: 'ไม่สามารถสร้างผู้ใช้งานได้',
      description: users.add.status.errorData.message || 'เกิดข้อผิดพลาด กรุณาลองใหม่อีกครั้ง',
    })

    dialog.close()
  },
)
</script>
