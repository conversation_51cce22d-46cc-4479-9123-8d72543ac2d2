<template>
  <aside
    :class="[`
      flex h-full flex-col overflow-y-auto border-r border-gray-100 bg-white
      transition-all duration-300
    `]"
  >
    <div
      :class="['flex h-[72px] items-center p-5', {
        'justify-center': isCollapsed,
        'justify-between': !isCollapsed,
      }]"
    >
      <NuxtLink
        v-if="!isCollapsed"
        :to="routes.home.to"
        class="flex gap-1"
      >
        <img
          v-if="!isCollapsed"
          src="/logo.png"
          alt="logo"
          class="h-[42px]"
        />
        <div>
          <div class="text-primary text-2xl leading-6 font-bold">CSP</div>
          <div class="text-xs font-bold">
            Cloud Sale Portal
          </div>
        </div>
      </NuxtLink>

      <Icon
        v-if="!isMobile"
        name="material-symbols:right-panel-open-outline"
        class="size-[24px] cursor-pointer text-gray-500"
        @click="$emit('toggle-collapsed')"
      />
      <Icon
        v-else
        name="ph:x-bold"
        class="size-[24px] cursor-pointer text-gray-500"
        @click="$emit('toggle-collapsed')"
      />
    </div>
    <div class="flex-1 overflow-y-auto px-5 pt-6">
      <NavigationMenu
        orientation="vertical"
        :items="sidebarUser"
        :collapsed="isCollapsed"
        :popover="isCollapsed"
        :tooltip="isCollapsed"
        class="w-full justify-center"
        :ui="{
          label: [
            'text-base text-gray-500 font-normal py-[12px] px-[10px] rounded-lg',
            'hover:bg-primary hover:text-white',
          ],
          link: [
            'cursor-pointer text-base text-gray-500 font-normal py-2 px-[10px] rounded-lg',
            'hover:bg-primary hover:text-white',
            'data-active:before:bg-primary data-active:before:rounded-lg data-active:text-white font-semibold',
          ],
          linkLeadingIcon: 'group-data-[state=open]:text-current text-current size-[24px] group-hover:text-white',
          childList: 'border-none ms-0 pl-8 bg-gray-100 mt-2 py-1 rounded-lg',
          childLink: 'ps-0',
          childItem: 'ps-0',
        }"
      />
      <div v-if="auth.isAdmin">
        <hr class="border-0.5 mt-4 mb-6 border-[#d9d9d9]" />

        <div class="mt-4 mb-2 text-[10px] font-semibold text-gray-400">
          ADMIN AREA
        </div>
        <NavigationMenu
          orientation="vertical"
          :items="sidebarAdmin"
          :collapsed="isCollapsed"
          :popover="isCollapsed"
          :tooltip="isCollapsed"
          class="w-full justify-center"
          :ui="{
            label: [
              'text-base text-gray-500 font-normal py-[12px] px-[10px] rounded-lg',
              'hover:bg-primary hover:text-white',
            ],
            link: [
              'cursor-pointer text-base text-gray-500 font-normal py-2 px-[10px] rounded-lg',
              'hover:bg-primary hover:text-white',
              'data-active:before:bg-primary data-active:before:rounded-lg data-active:text-white font-semibold',
            ],
            linkLeadingIcon: 'group-data-[state=open]:text-current text-current size-[24px] group-hover:text-white',
            childList: 'border-none ms-0 pl-8 bg-gray-100 mt-2 py-1 rounded-lg',
            childLink: 'ps-0',
            childItem: 'ps-0',
          }"
        />
      </div>
    </div>
    <div
      v-if="isMobile"
      class="border-t border-gray-100 p-3"
    >
      <div class="flex items-center justify-between gap-2">
        <div class="flex min-w-0 flex-1 items-center gap-3">
          <Avatar
            class="border-muted size-[32px] flex-shrink-0 border text-lg uppercase"
            :alt="auth.me.value?.display_name || auth.me.value?.email"
          />
          <div class="flex min-w-0 flex-1 flex-col">
            <p class="truncate text-sm font-bold">
              {{ auth.me.value?.username }}
            </p>
            <p class="text-muted truncate text-xs">
              {{ auth.me.value?.email }}
            </p>
          </div>
        </div>
        <DropdownMenu
          arrow
          size="xl"
          :items="userMenuItems"
          :ui="{
            content: 'w-48',
          }"
        >
          <Button
            icon="ph:dots-three-outline-vertical-bold"
            variant="ghost"
            color="neutral"
            size="xs"
          />
        </DropdownMenu>
      </div>
    </div>
  </aside>
</template>

<script lang="ts" setup>
import type { NavigationMenuItem } from '@nuxt/ui'
import { computed } from 'vue'
import { useRoute } from 'vue-router'
import { sidebarAdmin, sidebarUser } from '~/constants/routes'

interface Props {
  isCollapsed: boolean
  isMobile?: boolean
}

const props = defineProps<Props>()

const emit = defineEmits<{
  'toggle-collapsed': []
}>()

const auth = useAuth()

const route = useRoute()

const userMenuItems = [

  {
    label: 'ออกจากระบบ',
    icon: 'i-lucide-log-out',
    to: '/api/auth/logout',
    external: true,
  },
]

const navigationItems = computed<NavigationMenuItem[]>(() => {
  return sidebarAdmin.map((item) => {
    let isAnyChildActive = false
    const mappedChildren = item.children?.map((child) => {
      const isChildCurrentlyActive = route.path === child.to

      if (isChildCurrentlyActive) {
        isAnyChildActive = true
      }

      return {
        active: isChildCurrentlyActive,
        class: 'hover:bg-transparent hover:text-gray-700 hover:font-bold py-2 data-active:before:bg-transparent data-active:text-gray-700 data-active:font-bold',
        icon: '',
        ...child,
      }
    })

    const selfIsActive = item.to ? route.path.startsWith(String(item.to)) : false

    let itemIsActive = selfIsActive || isAnyChildActive // A root item is active if its own link matches OR if any child is active

    if (item.to === '/admin' && route.path !== '/admin') {
      itemIsActive = false // Ensure the root item is not active if the current path is not exactly '/'
    }

    const itemDefaultOpen = item.children ? isAnyChildActive : false

    return {
      ...item,
      active: itemIsActive,
      class: itemIsActive
        ? 'before:bg-primary before:rounded-lg text-white'
        : '',
      defaultOpen: itemDefaultOpen || selfIsActive,
      open: itemDefaultOpen || selfIsActive,
      children: mappedChildren,
      to: mappedChildren ? undefined : item.to,
    }
  })
})
</script>
