<template>
  <NuxtLayout>
    <Departments />
  </NuxtLayout>
</template>

<script lang="ts" setup>
import { routes } from '~/constants/routes'
import Departments from '~/features/organizations/Departments/index.vue'

definePageMeta({
  middleware: ['admin'],
  layout: 'admin',
})

useSeoMeta({
  title: routes.organization.departments.label,
})

useApp().definePage({
  title: routes.organization.departments.label,
  breadcrumbs: [routes.organization.departments],
})
</script>
