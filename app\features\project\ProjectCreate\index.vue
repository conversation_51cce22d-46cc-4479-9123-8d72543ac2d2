<template>
  <Form
    @submit="onSubmit"
  >
    <Card>
      <div class="mb-5 text-lg font-bold">
        ข้อมูลโครงการ
      </div>

      <FormFields
        class="mb-4 grid gap-3 md:grid-cols-2"
        :options="formProjectCode"
      />
      <FormFields
        class="mb-4 grid gap-3"
        :options="formNameProject"
      />
      <FormFields
        class="grid gap-3 md:grid-cols-3"
        :options="formField"
      />
      <FormFields
        class="grid gap-3 md:grid-cols-2"
        :options="formBudget"
      />
    </Card>
    <div
      class="mt-2 flex w-full justify-end"
    >
      <Button
        type="submit"
        class="mt-6"
        icon="material-symbols:check"
      >
        บันทึก
      </Button>
    </div>
  </Form>
</template>

<script lang="ts" setup>
const noti = useNotification()
const createProject = useProjectPageLoader()
const dialog = useDialog()
const ministry = useOrgMinistryPageLoader()
const department = useOrgDepartmentPageLoader()
const division = useOrgDivisionPageLoader()
const router = useRouter()
const form = useForm({
  validationSchema: toTypedSchema(v.object({
    code: v.optional(v.pipe(v.string(), v.nonEmpty('กรุณากรอกรหัสโครงการ')), ''),
    name: v.optional(v.pipe(v.string(), v.nonEmpty('กรุณากรอกชื่อโครงการ')), ''),
    ministry_id: v.optional(v.pipe(v.string(), v.nonEmpty('กรุณากรอกกระทรวง')), ''),
    // ministry_name: v.optional(v.pipe(v.string(), v.nonEmpty('กรอกกระทรวง')), ''),
    department_id: v.optional(v.pipe(v.string(), v.nonEmpty('กรุณากรอกกรม')), ''),
    // department_name: v.optional(v.pipe(v.string(), v.nonEmpty('กรอกกรม')), ''),
    division_id: v.optional(v.pipe(v.string(), v.nonEmpty('กรุณากรอกหน่วยงาน')), ''),
    // division_name: v.optional(v.pipe(v.string(), v.nonEmpty('กรอกหน่วยงาน')), ''),
    // contact_name: v.optional(v.pipe(v.string(), v.nonEmpty('กรุณากรอกรายละเอียด')), ''),
    // contact_phone: v.optional(v.pipe(v.string(), v.nonEmpty('กรุณากรอกวัตถุประสงค์')), ''),
    // contact_email: v.optional(v.pipe(v.string(), v.nonEmpty('กรุณากรอกขอบเขตของโครงการ')), ''),
    budget: v.optional(v.pipe(v.number(), v.minValue(1)), 0),
  })),
  keepValuesOnUnmount: true,
})

ministry.fetchSetLoading()
onMounted(() => {
  ministry.fetchPage()
})

const formProjectCode = createFormFields(() => [
  {
    type: INPUT_TYPES.TEXT,
    props: {
      name: 'code',
      label: 'รหัสโครงการ',
      required: true,
      autoFocus: true,
    },
  },
])

const formNameProject = createFormFields(() => [
  {
    type: INPUT_TYPES.TEXT,
    props: {
      name: 'name',
      label: 'ชื่อโครงการ',
      required: true,

    },
  },

])

const formField = createFormFields(() => [
  {
    type: INPUT_TYPES.SELECT,
    props: {
      required: true,
      loading: !ministry.fetch.status.isLoaded,
      label: 'กระทรวง',
      name: 'ministry_id',
      options: ministry.fetch.items?.map((item) => ({
        label: item.name_th,
        value: item.id,
      })) ?? [],
    },
    on: {
      change: (value: string) => {
        form.setFieldValue('department_id', '')
        form.setFieldValue('division_id', '')

        fetchDepartment(value)
      },
    },
  },
  {
    type: INPUT_TYPES.SELECT,
    props: {
      required: true,
      label: 'กรม',
      name: 'department_id',
      disabled: !form.values.ministry_id,
      options:
      department.fetch.items?.map((item: any) => ({
        label: item.name_th,
        value: item.id,
      })) ?? [],
    },
    on: {
      change: (value: string) => {
        form.setFieldValue('division_id', '')

        if (value && form.values.ministry_id) {
          fetchDivision(value, form.values.ministry_id)
        }
      },
    },
  },
  {
    type: INPUT_TYPES.SELECT,
    props: {
      required: true,
      label: 'หน่วยงาน',
      name: 'division_id',
      disabled: !form.values.department_id,
      options:
      division.fetch.items?.map((item: any) => ({
        label: item.name_th,
        value: item.id,
      })) ?? [],
    },
  },

])

const formBudget = createFormFields(() => [

  {
    type: INPUT_TYPES.NUMBER,
    props: {
      name: 'budget',
      label: 'งบประมาณ',
      required: true,

    },
  },
])

const onSubmit = form.handleSubmit((values) => {
  dialog.confirm({
    title: `ยืนยันการส่งคำขอสร้างโครงการใช่หรือไม่`,
  }).then(() => {
    createProject.addRun({
      data: values,
    })

    dialog.loading({
      title: 'กรุณารอสักครู่...',
      description: 'กำลังส่งข้อมูล...',
    })
  })
}, moveToError)

const fetchDepartment = (ministry_id: string) => {
  department.fetchPage(1, '', {
    params: {
      ministry_id: ministry_id,
    },
  })
}

const fetchDivision = (department_id: string, ministry_id: string) => {
  division.fetchPage(1, '', {
    params: {
      ministry_id: ministry_id,
      department_id: department_id,
    },
  })
}

useWatchTrue(
  () => createProject.add.status.isSuccess,
  () => {
    dialog.close()
    noti.success({
      title: 'ร่างโครงการสำเร็จ',

    })

    router.push(routes.project.projectById(createProject.add.item?.id || '1').to)
  },
)

useWatchTrue(
  () => createProject.add.status.isError,
  () => {
    noti.error({
      title: 'ไม่สามารถส่งคำขอสร้างโครงการได้',
      description: createProject.add.status.errorData.message || 'เกิดข้อผิดพลาด กรุณาลองใหม่อีกครั้ง',
    })

    dialog.close()
  },
)
</script>
