<template>
  <div class="mx-auto max-w-md pt-[40px]">
    <div class="text-center text-2xl font-bold">
      เปลี่ยนรหัสผ่าน
    </div>
    <div class="mt-5 mr-auto mb-2 text-lg font-bold">
      กำหนดรหัสผ่าน
    </div>
    <Form
      class="w-full"
      @submit="onSubmit"
    >
      <FormFields
        :form="form"
        :options="fields"
      />
      <div class="mt-[10px] rounded-sm bg-gray-100 p-4">
        <div
          v-for="rule in passwordRules"
          :key="rule.message"
          class="flex items-center gap-1"
        >
          <Icon
            v-if="!isRuleSatisfied(rule)"
            name="mdi:close-circle"
            class="size-4 min-w-4"
          />
          <Icon
            v-else
            name="mdi:check-circle"
            class="text-success size-4 min-w-4"
          />
          <div>{{ rule.message }}</div>
        </div>
      </div>

      <Button
        block
        type="submit"
        class="mt-5 justify-center"
        :disabled="!allRulesSatisfied"
        :loading="changePassword.status.value.isLoading"
        @click="onSubmit"
      >
        ยืนยันการเปลี่ยนรหัสผ่าน
      </Button>
    </Form>
  </div>
</template>

<script lang="ts" setup>
import * as v from 'valibot'
import { computed } from 'vue'
import { toTypedSchema } from '@vee-validate/valibot'
import { useForm } from 'vee-validate'
import { createFormFields } from '#core/composables/useForm'
import { INPUT_TYPES } from '#core/components/Form/types'

const router = useRouter()
const changePassword = useRegisterChangePasswordLoader()
const passwordRules = [
  {
    regex: /.{8,}/,
    message: 'รหัสผ่านต้องมีอย่างน้อย 8 ตัวอักษร',
  },
  {
    regex: /[a-z]/,
    message: 'ต้องมีตัวอักษรพิมพ์เล็กอย่างน้อย 1 ตัว (a-z)',
  },
  {
    regex: /[A-Z]/,
    message: 'ต้องมีตัวอักษรพิมพ์ใหญ่อย่างน้อย 1 ตัว (A-Z)',
  },
  {
    regex: /\d/,
    message: 'ต้องมีตัวเลขอย่างน้อย 1 ตัว (0-9)',
  },
  {
    regex: /[@#$%^&*!]/,
    message: 'ต้องมีอักขระพิเศษอย่างน้อย 1 ตัว (@#$%^&*! )',
  },
]

const form = useForm({
  validationSchema: toTypedSchema(
    v.object({
      old_password: v.optional(v.pipe(v.string(), v.nonEmpty()), ''),
      new_password: v.optional(v.pipe(v.string(), v.nonEmpty()), ''),
      confirm_password: v.optional(v.pipe(v.string(), v.nonEmpty()), ''),
    }),
  ),
})

const isRuleSatisfied = (rule: {
  regex: RegExp
  message: string
}) => {
  const newPassword = form.values.new_password

  return newPassword && rule.regex.test(newPassword)
}

const allRulesSatisfied = computed(() => {
  return passwordRules.every((rule) => isRuleSatisfied(rule))
})

const fields = createFormFields(() => [
  {
    type: INPUT_TYPES.PASSWORD,
    props: {
      label: 'รหัสผ่านเดิม',
      name: 'old_password',
      required: true,
    },
  },
  {
    type: INPUT_TYPES.PASSWORD,
    props: {
      label: 'รหัสผ่านใหม่',
      name: 'new_password',
      required: true,
    },
  },
  {
    type: INPUT_TYPES.PASSWORD,
    props: {
      label: 'ยืนยันรหัสผ่านใหม่',
      name: 'confirm_password',
      required: true,
    },
  },
])

const onSubmit = form.handleSubmit((values) => {
  changePassword.run({
    data: values,
  })
})

useWatchTrue(() => changePassword.status.value.isSuccess, () => {
  router.push(routes.login.to)
})
</script>
