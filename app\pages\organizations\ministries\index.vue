<template>
  <NuxtLayout>
    <Ministries />
  </NuxtLayout>
</template>

<script lang="ts" setup>
import { routes } from '~/constants/routes'
import Ministries from '~/features/organizations/Ministries/index.vue'

definePageMeta({
  middleware: ['admin'],
  layout: 'admin',
})

useSeoMeta({
  title: routes.organization.ministries.label,
})

useApp().definePage({
  title: routes.organization.ministries.label,
  breadcrumbs: [routes.organization.ministries],
})
</script>
