<template>
  <Modal
    :close="{ onClick: () => emits('close', false) }"
    :dismissible="false"
  >
    <template #content>
      <div class="p-5">
        <div class="text-center text-3xl font-bold">
          เพิ่มผู้ใช้งานสำเร็จ
        </div>
        <p
          class="pt-6 pb-2 text-center text-gray-500"
        >
          รหัสผ่านสำหรับการเข้าใช้งานของ <EMAIL>
        </p>
        <div
          class="mt-1 flex items-center justify-between bg-[#d9d9d9] p-4"
          @click="copyId"
        >
          <div class="font-bold">
            {{ id }}
          </div>
          <Icon name="i-lucide-copy" />
        </div>
        <div class="mt-4 flex justify-center">
          <Button
            icon="material-symbols:check"
            @click="onClose"
          >
            ตกลง
          </Button>
        </div>
      </div>
    </template>
  </Modal>
</template>

<script lang="ts" setup>
const emits = defineEmits<{ close: [boolean] }>()
const noti = useNotification()
const props = defineProps<{
  id: string
  onClose: () => void
}>()

const copyId = async () => {
  if (props.id) {
    await navigator.clipboard.writeText(props.id)
    noti.success({
      title: 'คัดลอกรหัสสำเร็จ',
    })
  }
}
</script>
