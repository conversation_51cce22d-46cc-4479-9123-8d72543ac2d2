<template>
  <NuxtLayout>
    <ProjectCreate />
  </NuxtLayout>
</template>

<script lang="ts" setup>
import ProjectCreate from '~/features/project/ProjectCreate/index.vue'

definePageMeta({
  middleware: ['admin'],
  layout: 'admin',
})

useSeoMeta({
  title: routes.project.projectCreate.label,
})

useApp().definePage({
  title: routes.project.projectCreate.label,
  breadcrumbs: [routes.project.projects, routes.project.projectCreate],
})
</script>
