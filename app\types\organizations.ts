import type { IUser } from './auth'

export interface IMinistry {
  id: string
  created_at: string
  updated_at: string
  deleted_at: any
  name_th: string
  name_en: string
  code: number
  short_name_th: string
  short_name_en: string
  department: IDepartment[]
}

export interface IDepartment {
  id: string
  created_at: string
  updated_at: string
  deleted_at: any
  ministry_id: string
  name_th: string
  name_en: string
  short_name_th?: string
  short_name_en?: string
  code: number
  division: IDivision[]
}

export interface IDivision {
  id: string
  created_at: string
  updated_at: string
  deleted_at: any
  ministry_id: string
  department_id: string
  name_th: string
  name_en?: string
  short_name_th?: string
  short_name_en?: string
  code: number
}

export interface IOrgRequest {
  id: string
  created_at: string
  updated_at: string
  deleted_at: any
  create_by: string
  ministry_id?: string
  department_id?: string
  division_id?: string
  ministry_name_th?: string
  ministry_name_en?: string
  department_name_th?: string
  department_name_en?: string
  division_name_th?: string
  division_name_en?: string
  file_name: string
  file_extension: string
  file_url: string
  status: string
  division?: IDivision
  ministry?: IMinistry
  department?: IDepartment
  user: IUser
}
