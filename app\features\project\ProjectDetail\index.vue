<template>
  <div
    v-if="!isEdit"
  >
    <Card>
      <InfoItemList
        :items="organizationItems"
        vertical
      />
    </Card>

    <div class="mt-6 flex w-full justify-between">
      <Button
        icon="icon-park-outline:left"
        variant="outline"
        color="neutral"
        :to="routes.project.projects.to"
      >
        กลับ
      </Button>
      <Button
        color="primary"
        @click="isEdit = true"
      >
        แก้ไขโครงการ
      </Button>
    </div>
  </div>
  <div v-else>
    <Form @submit="onSubmit">
      <Card>
        <FormFields
          :options="formNameProject"
        />
      </Card>
      <div
        class="mt-6 flex w-full justify-between"
      >
        <Button
          variant="outline"
          color="neutral"
          @click="isEdit = false"
        >
          ยกเลิกการแก้ไข
        </Button>
        <Button
          type="submit"
        >
          ยืนยัน
        </Button>
      </div>
    </Form>
  </div>
</template>

<script lang="ts" setup>
const route = useRoute()
const id = route.params.id as string
const project = useProjectPageLoader()
const noti = useNotification()
const createProject = useProjectPageLoader()
const dialog = useDialog()
const isEdit = ref(false)

project.findSetLoading()
onMounted(() => {
  project.findRun(id)
})

const form = useForm({
  validationSchema: toTypedSchema(v.object({
    name: v.optional(v.pipe(v.string(), v.nonEmpty('กรุณากรอกชื่อโครงการ')), ''),
    ministry_id: v.optional(v.pipe(v.string(), v.nonEmpty('กรุณากรอกกระทรวง')), ''),
    ministry_name: v.optional(v.pipe(v.string(), v.nonEmpty('กรอกกระทรวง')), ''),
    department_id: v.optional(v.pipe(v.string(), v.nonEmpty('กรุณากรอกกรม')), ''),
    department_name: v.optional(v.pipe(v.string(), v.nonEmpty('กรอกกรม')), ''),
    division_id: v.optional(v.pipe(v.string(), v.nonEmpty('กรุณากรอกหน่วยงาน')), ''),
    division_name: v.optional(v.pipe(v.string(), v.nonEmpty('กรอกหน่วยงาน')), ''),
    contact_name: v.optional(v.pipe(v.string(), v.nonEmpty('กรุณากรอกรายละเอียด')), ''),
    contact_phone: v.optional(v.pipe(v.string(), v.nonEmpty('กรุณากรอกวัตถุประสงค์')), ''),
    contact_email: v.optional(v.pipe(v.string(), v.nonEmpty('กรุณากรอกขอบเขตของโครงการ')), ''),
    budget: v.optional(v.pipe(v.number(), v.minValue(1)), 0),
  })),
  keepValuesOnUnmount: true,
})

const formNameProject = createFormFields(() => [
  {
    type: INPUT_TYPES.TEXT,
    props: {
      name: 'name',
      label: 'ชื่อโครงการ',
      required: true,
      autoFocus: true,
    },
  },
  {
    type: INPUT_TYPES.SELECT,
    props: {
      name: 'ministry_id',
      label: 'กระทรวง',
      options: [
        {
          value: '1',
          label: 'Option 1',
        },
        {
          value: '2',
          label: 'Option 2',
        },
      ],
      required: true,
    },
  },
  {
    type: INPUT_TYPES.SELECT,
    props: {
      name: 'department_id',
      label: 'กรม',
      required: true,
      options: [
        {
          value: '1',
          label: 'Option 1',
        },
        {
          value: '2',
          label: 'Option 2',
        },
      ],
    },
  },
  {
    type: INPUT_TYPES.SELECT,
    props: {
      name: 'division_id',
      label: 'หน่วยงาน',
      required: true,
      options: [
        {
          value: '1',
          label: 'Option 1',
        },
        {
          value: '2',
          label: 'Option 2',
        },
      ],
    },
    onChange: (value: any) => {
      form.setFieldValue('division_name', value.label)
    },
  },
  {
    type: INPUT_TYPES.TEXT,
    props: {
      name: 'contact_name',
      label: 'ชื่อผู้ติดต่อ',
      required: true,
    },
  },
  {
    type: INPUT_TYPES.TEXT,
    props: {
      name: 'contact_phone',
      label: 'เบอร์โทรศัพท์ผู้ติดต่อ',
      required: true,
    },
  },
  {
    type: INPUT_TYPES.TEXT,
    props: {
      name: 'contact_email',
      label: 'อีเมลผู้ติดต่อ',
      required: true,
    },
  },
  {
    type: INPUT_TYPES.NUMBER,
    props: {
      name: 'budget',
      label: 'งบประมาณ',
      required: true,
    },
  },
])

const organizationItems = computed(() => {
  return [
    {
      label: 'ชื่อโครงการ',
      value: project.find.item?.name,
    },
    {
      label: 'กระทรวง',
      value: project.find.item?.ministry_name,
    },
    {
      label: 'กรม',
      value: project.find.item?.department_name,
    },
    {
      label: 'หน่วยงาน',
      value: project.find.item?.division_name,
    },
    {
      label: 'ชื่อผู้ติดต่อ',
      value: project.find.item?.contact_name,
      max: 200,
    },
    {
      label: 'เบอร์โทรศัพท์ผู้ติดต่อ',
      value: project.find.item?.contact_phone,
      max: 200,
    },
    {
      label: 'อีเมลผู้ติดต่อ',
      value: project.find.item?.contact_email,
      max: 200,
    },
    {
      label: 'งบประมาณ',
      value: project.find.item?.budget,
      max: 200,
    },
  ]
})

const onSubmit = form.handleSubmit((values) => {
  dialog.confirm({
    title: `ยืนยันการส่งคำขอสร้างโครงการใช่หรือไม่`,
  }).then(() => {
    createProject.addRun({
      data: {
        ...project.find.item,
      },
    })

    dialog.loading({
      title: 'กรุณารอสักครู่...',
      description: 'กำลังส่งข้อมูล...',
    })
  })
}, moveToError)

useWatchTrue(
  () => createProject.add.status.isSuccess,
  () => {
    dialog.close()
  },
)

useWatchTrue(
  () => createProject.add.status.isError,
  () => {
    noti.error({
      title: 'ไม่สามารถส่งคำขอสร้างโครงการได้',
      description: createProject.add.status.errorData.message || 'เกิดข้อผิดพลาด กรุณาลองใหม่อีกครั้ง',
    })

    dialog.close()
  },
)

useSeoMeta({
  title: routes.project.projectById(id, project.find.item?.name).label,
})

useApp().definePage({
  title: routes.project.projectById(id).label,
  breadcrumbs: [routes.project.projects, routes.project.projectById(id)],
})
</script>
