<template>
  <div
    class="flex flex-col"
  >
    <p>{{ getValue.date }}</p>
    <p>{{ getValue.time }}</p>
  </div>
</template>

<script lang="ts" setup>
import type { TableColumn } from '@nuxt/ui'

const props = defineProps<{
  value: string | Date
  row: any
  column: TableColumn<any>
}>()

const getValue = computed(() => {
  if (!props.value) {
    return {
      date: '-',
      time: '',
    }
  }

  const datetime = TimeHelper.displayDateTime(props.value).split(' ')
  const date = datetime[0]
  const time = date ? `${datetime[1]} ${datetime[2]}` : ''

  return {
    date: date || '-',
    time: time,
  }
})
</script>
