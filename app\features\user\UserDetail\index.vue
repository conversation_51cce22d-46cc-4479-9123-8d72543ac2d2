<template>
  <Form
    @submit="onSubmit"
  >
    <Card>
      <div class="mb-5 text-lg font-bold">
        ข้อมูลผู้ใช้งาน
      </div>

      <FormFields
        class="mb-4 grid gap-3 md:grid-cols-2"
        :options="formProjectCode"
      />
      <FormFields
        class="grid gap-3"
        :options="formNameProject"
      />
    </Card>
    <div
      class="mt-2 flex w-full justify-between"
    >
      <Button
        class="mt-6"
        icon="material-symbols:lock-reset"
        @click="resetPassword"
      >
        รีเซ็ตรหัสผ่านใหม่
      </Button>
      <Button
        type="submit"
        class="mt-6"
        icon="material-symbols:check"
      >
        บันทึก
      </Button>
    </div>
  </Form>
</template>

<script lang="ts" setup>
import Password from '../Password.vue'

const route = useRoute()
const id = route.params.id as string
const noti = useNotification()
const dialog = useDialog()
const users = useUserPageLoader()
const isPassword = ref('')
const overlay = useOverlay()
const userResetPassword = useUserResetPasswordPageLoader()
const passwordModal = overlay.create(Password)

users.fetchSetLoading()
onMounted(() => {
  users.findRun(id)
})

const form = useForm({
  validationSchema: toTypedSchema(v.object({
    display_name: v.optional(v.pipe(v.string(), v.nonEmpty('กรุณากรอกชื่อผู้ใช้งาน')), ''),
    email: v.optional(v.pipe(v.string(), v.nonEmpty('กรุณากรอกอีเมล')), ''),
    type: v.optional(v.pipe(v.string(), v.nonEmpty('กรุณาเลือกประเภทผู้ใช้งาน')), ''),
  })),
  keepValuesOnUnmount: true,
})

const formProjectCode = createFormFields(() => [
  {
    type: INPUT_TYPES.TEXT,
    props: {
      name: 'display_name',
      label: 'ชื่อในการแสดงผล',
      required: true,
    },
  },
  {
    type: INPUT_TYPES.TEXT,
    props: {
      name: 'email',
      label: 'อีเมล',
      required: true,
      disabled: true,
    },
  },
])

const formNameProject = createFormFields(() => [
  {
    type: INPUT_TYPES.RADIO,
    props: {
      name: 'type',
      label: 'ประเภทผู้ใช้งาน',
      required: true,
      options: [
        {
          value: UserType.ADMIN,
          label: 'Admin',
        },
        {
          value: UserType.USER,
          label: 'User',
        },
      ],
    },
  },

])

const generatePassword = (length = 10): string => {
  const upperChars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ'
  const lowerChars = 'abcdefghijklmnopqrstuvwxyz'
  const numberChars = '0123456789'
  const specialChars = '!@#$%^&*()_+-=[]{}|;:,.<>?'

  const upper = upperChars.charAt(Math.floor(Math.random() * upperChars.length))
  const lower = lowerChars.charAt(Math.floor(Math.random() * lowerChars.length))
  const number = numberChars.charAt(Math.floor(Math.random() * numberChars.length))
  const special = specialChars.charAt(Math.floor(Math.random() * specialChars.length))

  const allChars = upperChars + lowerChars + numberChars + specialChars

  const remainingLength = length - 4
  let password = upper + lower + number + special

  for (let i = 0; i < remainingLength; i++) {
    password += allChars[Math.floor(Math.random() * allChars.length)]
  }

  return password
    .split('')
    .sort(() => Math.random() - 0.5)
    .join('')
}

const resetPassword = () => {
  dialog.confirm({
    title: `ยืนยันจะรีเซ็ตพาสเวิร์ดใช่หรือไม่`,
  }).then(() => {
    isPassword.value = generatePassword()

    userResetPassword.run({
      data: {
        new_password: isPassword.value,
      },
      urlBind: {
        id: id,
      },
    })

    dialog.loading({
      title: 'กรุณารอสักครู่...',
      description: 'กำลังส่งข้อมูล...',
    })
  })
}

const onSubmit = form.handleSubmit((values) => {
  dialog.confirm({
    title: `ยืนยันอัพเดทผู้ใช้งาน`,
  }).then(() => {
    users.updateRun(id, {
      data: values,
    })

    dialog.loading({
      title: 'กรุณารอสักครู่...',
      description: 'กำลังส่งข้อมูล...',
    })
  })
}, moveToError)

useWatchTrue(
  () => users.update.status.isSuccess,
  () => {
    dialog.close()
    noti.success({
      title: 'อัพเดทผู้ใช้งานสำเร็จ',
    })
  },
)

useWatchTrue(
  () => users.update.status.isError,
  () => {
    noti.error({
      title: 'ไม่สามารถอัพเดทผู้ใช้งานได้',
      description: users.update.status.errorData.message || 'เกิดข้อผิดพลาด กรุณาลองใหม่อีกครั้ง',
    })

    dialog.close()
  },
)

useWatchTrue(
  () => userResetPassword.status.value.isSuccess,
  () => {
    dialog.close()

    noti.success({
      title: 'รีเซทพาสเวิร์ดสำเร็จ',
    })

    passwordModal.open({
      id: isPassword.value,
      onClose: () => {
        passwordModal.close()
      },
    })
  },
)

useWatchTrue(
  () => userResetPassword.status.value.isError,
  () => {
    dialog.close()
    noti.error({
      title: 'ไม่สามารถรีเซทพาสเวิร์ดได้',
    })
  },
)

useWatchTrue(
  () => users.find.status.isSuccess,
  () => {
    form.setValues(users.find.item!)
  },
)

useSeoMeta({
  title: routes.user.userById(id, users.find.item?.display_name).label,
})

useApp().definePage({
  title: routes.user.userById(id).label,
  breadcrumbs: [routes.user.users, routes.user.userById(id)],
})
</script>
