import { defineEventHand<PERSON>, getQuery } from 'h3'

export default defineEventHandler((event) => {
  const query = getQuery(event)
  const {
    limit, page,
  } = query

  const items = [
    {
      id: '1',
      name: 'Digital Transformation',
      contact_name: '<PERSON>',
      contact_phone: '0812345678',
      contact_email: '<EMAIL>',
      budget: 5000000,
      ministry_id: 'MIN-001',
      ministry_name: 'Ministry of Digital Economy and Society',
      department_id: 'DEP-101',
      department_name: 'Department of Digital Transformation',
      division_id: 'DIV-201',
      division_name: 'Division of Digital Transformation',
      created_at: '2025-01-01T10:00:00Z',
      created_by: 'admin',
      updated_at: '2025-06-01T12:00:00Z',
      updated_by: 'admin',
      deleted_at: '',
      deleted_by: '',
    },
    {
      id: '2',
      name: 'Education Cloud Migration',
      contact_name: '<PERSON>',
      contact_phone: '0898765432',
      contact_email: '<EMAIL>',
      budget: 7500000,
      ministry_id: 'MIN-002',
      ministry_name: 'Ministry of Education',
      department_id: 'DEP-102',
      department_name: 'Department of IT in Education',
      division_id: 'DIV-202',
      division_name: 'Division of eLearning Systems',
      created_at: '2025-02-10T09:30:00Z',
      created_by: 'system',
      updated_at: '2025-07-01T08:00:00Z',
      updated_by: 'editor',
      deleted_at: '',
      deleted_by: '',
    },
    {
      id: '3',
      name: 'Smart Agriculture Pilot',
      contact_name: 'Chaiwat S.',
      contact_phone: '0871234567',
      contact_email: '<EMAIL>',
      budget: 3200000,
      ministry_id: 'MIN-003',
      ministry_name: 'Ministry of Agriculture and Cooperatives',
      department_id: 'DEP-103',
      department_name: 'Department of Agricultural Innovation',
      division_id: 'DIV-203',
      division_name: 'Division of Smart Farming',
      created_at: '2025-03-15T11:45:00Z',
      created_by: 'admin',
      updated_at: '2025-07-15T10:00:00Z',
      updated_by: 'admin',
      deleted_at: '',
      deleted_by: '',
    },
    {
      id: '4',
      name: 'Healthcare Data Platform',
      contact_name: 'Dr. Nicha',
      contact_phone: '**********',
      contact_email: '<EMAIL>',
      budget: 8600000,
      ministry_id: 'MIN-004',
      ministry_name: 'Ministry of Public Health',
      department_id: 'DEP-104',
      department_name: 'Department of Medical Technology',
      division_id: 'DIV-204',
      division_name: 'Division of Health Information Systems',
      created_at: '2025-04-05T13:00:00Z',
      created_by: 'health-admin',
      updated_at: '2025-07-25T14:00:00Z',
      updated_by: 'health-admin',
      deleted_at: '',
      deleted_by: '',
    },
    {
      id: '5',
      name: 'e-Government API Gateway',
      contact_name: 'Thitima P.',
      contact_phone: '**********',
      contact_email: '<EMAIL>',
      budget: 4400000,
      ministry_id: 'MIN-005',
      ministry_name: 'Ministry of Interior',
      department_id: 'DEP-105',
      department_name: 'Department of Provincial Administration',
      division_id: 'DIV-205',
      division_name: 'Division of Digital Infrastructure',
      created_at: '2025-05-01T10:15:00Z',
      created_by: 'it-admin',
      updated_at: '2025-07-30T09:45:00Z',
      updated_by: 'it-admin',
      deleted_at: '',
      deleted_by: '',
    },
  ]
    .map((value) => ({
      value,
      sort: Math.random(),
    }))
    .sort((a, b) => a.sort - b.sort)
    .map(({
      value,
    }) => value)

  return {
    page: +(page || 1),
    total: 200,
    limit: +(limit || 30),
    count: items.length,
    items,
  }
})
