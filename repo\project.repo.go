package repo

import (
	"gitlab.finema.co/finema/csp/csp-api/models"
	core "gitlab.finema.co/finema/idin-core"
	"gitlab.finema.co/finema/idin-core/repository"
)

type ProjectOption func(repository.IRepository[models.Project])

var Project = func(c core.IContext, options ...ProjectOption) repository.IRepository[models.Project] {
	r := repository.New[models.Project](c)
	for _, opt := range options {
		opt(r)
	}

	return r
}

func ProjectOrderBy(pageOptions *core.PageOptions) ProjectOption {
	return func(c repository.IRepository[models.Project]) {
		if len(pageOptions.OrderBy) == 0 {
			c.Order("created_at DESC")
		} else {
			c.Order(pageOptions.OrderBy)
		}
	}
}

func ProjectWithMinistry() ProjectOption {
	return func(c repository.IRepository[models.Project]) {
		c.Preload("Ministry")
	}
}

func ProjectWithDepartment() ProjectOption {
	return func(c repository.IRepository[models.Project]) {
		c.Preload("Department")
	}
}

func ProjectWithDivision() ProjectOption {
	return func(c repository.IRepository[models.Project]) {
		c.Preload("Division")
	}
}

func ProjectWithCreatedBy() ProjectOption {
	return func(c repository.IRepository[models.Project]) {
		c.Preload("CreatedBy")
	}
}

func ProjectWithUpdatedBy() ProjectOption {
	return func(c repository.IRepository[models.Project]) {
		c.Preload("UpdatedBy")
	}
}

func ProjectWithAllRelations() ProjectOption {
	return func(c repository.IRepository[models.Project]) {
		c.Preload("Ministry")
		c.Preload("Department")
		c.Preload("Division")
		c.Preload("CreatedBy")
		c.Preload("UpdatedBy")
	}
}

func ProjectByMinistry(ministryID string) ProjectOption {
	return func(c repository.IRepository[models.Project]) {
		c.Where("ministry_id = ?", ministryID)
	}
}

func ProjectByDepartment(departmentID string) ProjectOption {
	return func(c repository.IRepository[models.Project]) {
		c.Where("department_id = ?", departmentID)
	}
}

func ProjectByDivision(divisionID string) ProjectOption {
	return func(c repository.IRepository[models.Project]) {
		c.Where("division_id = ?", divisionID)
	}
}

func ProjectByCreatedBy(userID string) ProjectOption {
	return func(c repository.IRepository[models.Project]) {
		c.Where("created_by_id = ?", userID)
	}
}

func ProjectBySearch(search string) ProjectOption {
	if search == "" {
		return func(c repository.IRepository[models.Project]) {}
	}
	return func(c repository.IRepository[models.Project]) {
		searchPattern := "%" + search + "%"
		c.Where("name ILIKE ? OR contact_name ILIKE ? OR contact_email ILIKE ?", searchPattern, searchPattern, searchPattern)
	}
}
