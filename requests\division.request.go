package requests

import (
	"gitlab.finema.co/finema/csp/csp-api/models"
	core "gitlab.finema.co/finema/idin-core"
)

type DivisionCreate struct {
	core.BaseValidator
	MinistryID   *string `json:"ministry_id"`
	DepartmentID *string `json:"department_id"`
	NameTh       *string `json:"name_th"`
	NameEn       *string `json:"name_en"`
}

func (r *DivisionCreate) Valid(ctx core.IContext) core.IError {
	r.Must(r.IsStrRequired(r.MinistryID, "ministry_id"))
	r.Must(r.IsStrRequired(r.DepartmentID, "department_id"))
	r.Must(r.IsStrRequired(r.NameTh, "name_th"))

	// Check if ministry exists
	if r.MinistryID != nil {
		r.Must(r.IsExists(ctx, r.MinistryID, models.Ministry{}.TableName(), "id", "ministry_id"))
	}

	// Check if department exists
	if r.DepartmentID != nil {
		r.Must(r.IsExists(ctx, r.DepartmentID, models.Department{}.TableName(), "id", "department_id"))
	}
	return r.Error()
}

type DivisionUpdate struct {
	core.BaseValidator
	MinistryID   *string `json:"ministry_id"`
	DepartmentID *string `json:"department_id"`
	NameTh       *string `json:"name_th"`
	NameEn       *string `json:"name_en"`
}

func (r *DivisionUpdate) Valid(ctx core.IContext) core.IError {
	// Check if ministry exists if provided
	if r.MinistryID != nil {
		r.Must(r.IsExists(ctx, r.MinistryID, models.Ministry{}.TableName(), "id", "ministry_id"))
	}

	// Check if department exists if provided
	if r.DepartmentID != nil {
		r.Must(r.IsExists(ctx, r.DepartmentID, models.Department{}.TableName(), "id", "department_id"))
	}

	return r.Error()
}
