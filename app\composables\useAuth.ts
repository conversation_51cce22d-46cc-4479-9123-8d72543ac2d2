import type { IUser } from '~/types/user'

export const useAuth = () => {
  const {
    auth,
    base,
  } = useRequestOptions()

  const me = defineStore('auth.me', () => {
    const value = ref<IUser | null>(null)

    const set = (user: IUser | null) => {
      value.value = user
    }

    return {
      value,
      set,
    }
  })()

  const fetchMe = (() => {
    return useObjectLoader<IUser>({
      method: 'GET',
      url: '/me',
      getRequestOptions: (data) => {
        if (data?.token) {
          return {
            ...base(),
            headers: {
              Authorization: `Bearer ${data?.token}`,
            },
          }
        }

        return auth()
      },
    })
  })()

  const login = (() => {
    return useObjectLoader<{ token: string }>({
      method: 'POST',
      url: '/auth/login',
      getRequestOptions: base,
    })
  })()

  const token = useCookie<string | undefined>('authorization', {
    maxAge: 60 * 60 * 24 * 30, // 30 days
    path: '/',
  })

  const isAuthenticated = computed(() => {
    return !!token.value
  })

  const isAdmin = computed(() => {
    return me.value?.type === UserType.ADMIN
  })

  const isUser = computed(() => {
    return me.value?.type === UserType.USER
  })

  useWatchTrue(() => fetchMe.status.value.isSuccess, () => {
    me.set(fetchMe.data.value)
  })

  useWatchTrue(() => fetchMe.status.value.isError, () => {
    token.value = undefined
    me.set(null)
  })

  return {
    me,
    isAdmin,
    isUser,
    fetchMe,
    login,
    token,
    isAuthenticated,
  }
}
