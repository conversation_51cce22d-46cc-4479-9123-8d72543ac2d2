export const useUserPageLoader = () => {
  const options = useRequestOptions()

  return usePageLoader<IUser>({
    baseURL: '/users',
    getBaseRequestOptions: options.auth,
  })
}

export const useUserResetPasswordPageLoader = () => {
  const options = useRequestOptions()

  return useObjectLoader<IUser>({
    method: 'POST',
    url: '/users/:id/reset-password',
    getRequestOptions: options.auth,
  })
}
