import type { IMinistry, IDepartment, IDivision } from '~/types/organizations'

export const useOrgMinistryPageLoader = () => {
  const options = useRequestOptions()

  return usePageLoader<IMinistry>({
    baseURL: '/ministries',
    getBaseRequestOptions: options.auth,
  })
}

export const useOrgDepartmentPageLoader = () => {
  const options = useRequestOptions()

  return usePageLoader<IDepartment>({
    baseURL: '/departments',
    getBaseRequestOptions: options.auth,
  })
}

export const useOrgDivisionPageLoader = () => {
  const options = useRequestOptions()

  return usePageLoader<IDivision>({
    baseURL: '/divisions',
    getBaseRequestOptions: options.auth,
  })
}
