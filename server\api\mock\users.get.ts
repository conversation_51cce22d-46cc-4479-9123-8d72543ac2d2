import { define<PERSON><PERSON><PERSON><PERSON><PERSON>, getQuery } from 'h3'

export default defineEventHandler((event) => {
  const query = getQuery(event)
  const {
    limit, page,
  } = query

  const items: any = [
    {
      id: '1',
      display_name: 'User One',
      email: '<EMAIL>',
      type: 'ADMIN',
      password: 'password123',
      created_at: new Date().toISOString(),
      created_by: null,
      updated_at: new Date().toISOString(),
      updated_by: null,
      deleted_at: null,
    },
    {
      id: '1',
      display_name: 'User One',
      email: '<EMAIL>',
      type: 'USER',
      password: 'password123',
      created_at: new Date().toISOString(),
      created_by: null,
      updated_at: new Date().toISOString(),
      updated_by: null,
      deleted_at: null,
    },
    {
      id: '1',
      display_name: 'User One',
      email: '<EMAIL>',
      password: 'password123',
      type: 'ADMIN',
      created_at: new Date().toISOString(),
      created_by: null,
      updated_at: new Date().toISOString(),
      updated_by: null,
      deleted_at: null,
    },
    {
      id: '1',
      display_name: 'User One',
      email: '<EMAIL>',
      type: 'USER',
      password: 'password123',
      created_at: new Date().toISOString(),
      created_by: null,
      updated_at: new Date().toISOString(),
      updated_by: null,
      deleted_at: null,
    },
    {
      id: '1',
      display_name: 'User One',
      email: '<EMAIL>',
      type: 'ADMIN',
      password: 'password123',
      created_at: new Date().toISOString(),
      created_by: null,
      updated_at: new Date().toISOString(),
      updated_by: null,
      deleted_at: null,
    },
    {
      id: '1',
      display_name: 'User One',
      email: '<EMAIL>',
      type: 'ADMIN',
      password: 'password123',
      created_at: new Date().toISOString(),
      created_by: null,
      updated_at: new Date().toISOString(),
      updated_by: null,
      deleted_at: null,
    },
    {
      id: '1',
      display_name: 'User One',
      email: '<EMAIL>',
      type: 'ADMIN',
      password: 'password123',
      created_at: new Date().toISOString(),
      created_by: null,
      updated_at: new Date().toISOString(),
      updated_by: null,
      deleted_at: null,
    },
    {
      id: '1',
      display_name: 'User One',
      email: '<EMAIL>',
      type: 'ADMIN',
      password: 'password123',
      created_at: new Date().toISOString(),
      created_by: null,
      updated_at: new Date().toISOString(),
      updated_by: null,
      deleted_at: null,
    },
    {
      id: '1',
      display_name: 'User One',
      email: '<EMAIL>',
      type: 'ADMIN',
      password: 'password123',
      created_at: new Date().toISOString(),
      created_by: null,
      updated_at: new Date().toISOString(),
      updated_by: null,
      deleted_at: null,
    },
    {
      id: '1',
      display_name: 'User One',
      email: '<EMAIL>',
      type: 'ADMIN',
      password: 'password123',
      created_at: new Date().toISOString(),
      created_by: null,
      updated_at: new Date().toISOString(),
      updated_by: null,
      deleted_at: null,
    },
    {
      id: '1',
      display_name: 'User One',
      email: '<EMAIL>',
      type: 'ADMIN',
      password: 'password123',
      created_at: new Date().toISOString(),
      created_by: null,
      updated_at: new Date().toISOString(),
      updated_by: null,
      deleted_at: null,
    },

  ]

    .map((value) => ({
      value,
      sort: Math.random(),
    }))
    .sort((a, b) => a.sort - b.sort)
    .map(({
      value,
    }) => value)

  return {
    page: +(page || 1),
    total: 200,
    limit: +(limit || 30),
    count: items.length,
    items,
  }
})
