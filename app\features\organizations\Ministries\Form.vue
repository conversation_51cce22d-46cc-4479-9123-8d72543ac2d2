<template>
  <Modal
    :close="{ onClick: () => emits('close', false) }"
    :dismissible="false"
    :title="isEditing ? 'แก้ไขกระทรวง' : 'เพิ่มกระทรวง'"
  >
    <template #body>
      <form @submit="onSubmit">
        <FormFields :options="formFields" />
        <div class="mt-4 flex justify-end gap-3">
          <Button
            variant="outline"
            color="neutral"
            @click="emits('close', false)"
          >
            ยกเลิก
          </Button>
          <Button
            :loading="status().isLoading"
            :disabled="!form.meta.value.dirty"
            type="submit"
          >
            {{ isEditing ? 'บันทึก' : 'เพิ่ม' }}
          </Button>
        </div>
      </form>
    </template>
  </Modal>
</template>

<script lang="ts" setup>
const emits = defineEmits<{ close: [boolean] }>()

const props = defineProps<{
  isEditing?: boolean
  values?: IMinistry | null
  status: () => IStatus
  onSubmit: (values: IMinistry) => void
}>()

const form = useForm({
  initialValues: props.values,
  validationSchema: toTypedSchema(v.object({
    name_th: v.optional(v.pipe(v.string(), v.nonEmpty()), ''),
    name_en: v.optional(v.pipe(v.string(), v.nonEmpty()), ''),
  })),
})

const formFields = createFormFields(() => [
  {
    type: INPUT_TYPES.TEXT,
    props: {
      label: 'ชื่อกระทรวง (ภาษาไทย)',
      name: 'name_th',
      placeholder: 'กรุณากรอกชื่อกระทรวง (ภาษาไทย)',
      required: true,
      ...MaskaHelper.thaiLetter(),
    },
  },
  {
    type: INPUT_TYPES.TEXT,
    props: {
      label: 'ชื่อกระทรวง (ภาษาอังกฤษ)',
      name: 'name_en',
      placeholder: 'กรุณากรอกชื่อกระทรวง (ภาษาอังกฤษ)',
      required: true,
      ...MaskaHelper.englishLetter(),
    },
  },
])

const onSubmit = form.handleSubmit((values) => {
  props.onSubmit(values as IMinistry)
})
</script>
