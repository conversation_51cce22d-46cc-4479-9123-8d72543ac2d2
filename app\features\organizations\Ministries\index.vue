<template>
  <div>
    <TeleportSafe to="#page-header">
      <Button
        trailing-icon="basil:plus-solid"
        @click="addMinistry"
      >
        เพิ่มกระทรวง
      </Button>
    </TeleportSafe>
    <FormFields
      :form="form"
      :options="formFields"
      class="mb-4"
    />
    <Table
      :options="tableOptions"
      @pageChange="ministry.fetchPageChange"
      @search="ministry.fetchSearch"
    >
      <template #actions-cell="{ row }">
        <div class="flex">
          <ButtonActionIcon
            icon="mage:pen"
            color="neutral"
            @click="editMinistry(row.original)"
          />
          <ButtonActionIcon
            icon="prime:trash"
            color="error"
            :disabled="getCount(row.original).totalDepartments > 0 || getCount(row.original).totalDivisions > 0"
            @click="onDelete(row.original)"
          />
        </div>
      </template>
    </Table>
  </div>
</template>

<script lang="ts" setup>
import * as v from 'valibot'
import { watchDebounced } from '@vueuse/core'
import Form from './Form.vue'
import type { IMinistry } from '~/types/organizations'
import { useOrgMinistryPageLoader } from '~/loaders/organizations'
import { COLUMN_TYPES } from '#core/components/Table/types'
import { INPUT_TYPES } from '#core/components/Form/types'

const ministry = useOrgMinistryPageLoader()
const dialog = useDialog()
const noti = useNotification()
const overlay = useOverlay()
const editModal = overlay.create(Form)
const addModal = overlay.create(Form)

const form = useForm({
  validationSchema: toTypedSchema(v.object({
    q: v.optional(v.pipe(v.string()), ''),
  })),
})

const formFields = createFormFields(() => [
  {
    type: INPUT_TYPES.SEARCH,
    props: {
      name: 'q',
      placeholder: 'ค้นหากระทรวง',
    },
  },
])

watchDebounced(form.values, (values) => {
  ministry.fetchSearch(values.q || '')
}, {
  debounce: 300,
  deep: true,
})

const tableOptions = useTable<IMinistry>({
  repo: ministry,
  columns: () => [
    {
      accessorKey: 'name_th',
      header: 'ชื่อกระทรวง (ภาษาไทย)',
      type: COLUMN_TYPES.TEXT,
      meta: {
        max: 40,
      },
    },
    {
      accessorKey: 'name_en',
      header: 'ชื่อกระทรวง (ภาษาอังกฤษ)',
      type: COLUMN_TYPES.TEXT,
      meta: {
        max: 40,
      },
    },
    {
      header: 'จำนวนกรม',
      accessorKey: 'departments_count',
      type: COLUMN_TYPES.NUMBER,
      meta: {
        class: {
          th: 'text-right',
          td: 'text-right',
        },
      },
      cell: ({
        row,
      }) => {
        return getCount(row.original).totalDepartments
      },
    },
    {
      header: 'จำนวนหน่วยงาน',
      accessorKey: 'divisions_count',
      type: COLUMN_TYPES.NUMBER,
      meta: {
        class: {
          th: 'text-right',
          td: 'text-right',
        },
      },
      cell: ({
        row,
      }) => {
        return getCount(row.original).totalDivisions
      },
    },
    {
      accessorKey: 'actions',
      header: '',
    },
  ],
})

const getCount = (_ministry: IMinistry) => {
  const totalDepartments = _ministry?.department?.length || 0
  const totalDivisions = _ministry?.department?.map((d) => d.division.length || 0).reduce((a, b) => a + b, 0) || 0

  return {
    totalDepartments,
    totalDivisions,
  }
}

ministry.fetchSetLoading()
onMounted(() => {
  ministry.fetchPage()
})

const editMinistry = (_ministry: IMinistry) => {
  editModal.open({
    isEditing: true,
    values: _ministry,
    status: () => ministry.update.status,
    onSubmit: (values: IMinistry) => {
      ministry.updateRun(_ministry.id, {
        data: values,
      })
    },
  })
}

const addMinistry = () => {
  addModal.open({
    status: () => ministry.add.status,
    onSubmit: (values: IMinistry) => {
      ministry.addRun({
        data: values,
      })
    },
  })
}

useWatchTrue(
  () => ministry.update.status.isSuccess,
  () => {
    editModal.close()
    ministry.fetchPage()

    noti.success({
      title: 'แก้ไขกระทรวงสำเร็จ',
      description: 'คุณได้แก้ไขกระทรวงเรียบร้อยแล้ว',
    })
  },
)

useWatchTrue(
  () => ministry.update.status.isError,
  () => {
    noti.error({
      title: 'แก้ไขกระทรวงไม่สำเร็จ',
      description: 'เกิดข้อผิดพลาดในการแก้ไขกระทรวง กรุณาลองใหม่อีกครั้ง',
    })
  },
)

useWatchTrue(
  () => ministry.delete.status.isSuccess,
  () => {
    dialog.close()
    noti.success({
      title: 'ลบกระทรวงสำเร็จ',
      description: 'คุณได้ลบกระทรวงเรียบร้อยแล้ว',
    })
  },
)

useWatchTrue(
  () => ministry.delete.status.isError,
  () => {
    dialog.close()
    noti.error({
      title: 'ลบกระทรวงไม่สำเร็จ',
      description: 'เกิดข้อผิดพลาดในการลบกระทรวง กรุณาลองใหม่อีกครั้ง',
    })
  },
)

useWatchTrue(
  () => ministry.add.status.isSuccess,
  () => {
    addModal.close()
    ministry.fetchPage()

    noti.success({
      title: 'เพิ่มกระทรวงสำเร็จ',
      description: 'คุณได้เพิ่มกระทรวงเรียบร้อยแล้ว',
    })
  },
)

useWatchTrue(
  () => ministry.add.status.isError,
  () => {
    noti.error({
      title: 'เพิ่มกระทรวงไม่สำเร็จ',
      description: 'เกิดข้อผิดพลาดในการเพิ่มกระทรวง กรุณาลองใหม่อีกครั้ง',
    })
  },
)

const onDelete = (values: IMinistry) => {
  dialog.confirm({
    title: 'ยืนยันการลบ',
    description: `คุณต้องการลบกระทรวง "${values.name_th}" หรือไม่?`,
    confirmText: 'ยืนยัน',
    cancelText: 'ยกเลิก',
  }).then(() => {
    ministry.deleteRun(values.id)
    dialog.loading({
      title: 'กรุณารอสักครู่...',
      description: 'กำลังส่งข้อมูล...',
    })
  })
}
</script>
