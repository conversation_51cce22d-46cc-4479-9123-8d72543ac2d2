<template>
  <NuxtLayout>
    <UserCreate />
  </NuxtLayout>
</template>

<script lang="ts" setup>
import UserCreate from '~/features/user/UserCreate/index.vue'

definePageMeta({
  middleware: ['admin'],
  layout: 'admin',
})

useSeoMeta({
  title: routes.user.userCreate.label,
})

useApp().definePage({
  title: routes.user.userCreate.label,
  breadcrumbs: [routes.user.users, routes.user.userCreate],
})
</script>
