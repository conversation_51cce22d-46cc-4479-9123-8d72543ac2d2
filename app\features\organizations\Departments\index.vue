<template>
  <div>
    <TeleportSafe to="#page-header">
      <Button
        trailing-icon="basil:plus-solid"
        @click="onAdd"
      >
        เพิ่มกรม
      </Button>
    </TeleportSafe>
    <FormFields
      :form="form"
      :options="formFields"
      class="
        mb-4 grid w-full gap-4
        lg:grid-cols-4
      "
    />
    <Table
      :options="tableOptions"
      @pageChange="department.fetchPageChange"
      @search="department.fetchSearch"
    >
      <template #actions-cell="{ row }">
        <div class="flex">
          <ButtonActionIcon
            icon="mage:pen"
            color="neutral"
            @click="onEdit(row.original)"
          />
          <ButtonActionIcon
            icon="prime:trash"
            color="error"
            :disabled="getCount(row.original).totalDivisions > 0"
            @click="onDelete(row.original)"
          />
        </div>
      </template>
    </Table>
  </div>
</template>

<script lang="ts" setup>
import * as v from 'valibot'
import { watchDebounced } from '@vueuse/core'
import Form from './Form.vue'
import { COLUMN_TYPES } from '#core/components/Table/types'
import { useOrgDepartmentPageLoader, useOrgMinistryPageLoader } from '~/loaders/organizations'
import type { IDepartment } from '~/types/organizations'
import { INPUT_TYPES } from '#core/components/Form/types'

const department = useOrgDepartmentPageLoader()
const ministry = useOrgMinistryPageLoader()
const dialog = useDialog()
const noti = useNotification()
const overlay = useOverlay()
const editModal = overlay.create(Form)
const addModal = overlay.create(Form)

const form = useForm({
  validationSchema: toTypedSchema(v.object({
    q: v.optional(v.pipe(v.string()), ''),
    ministry_id: v.optional(v.pipe(v.string()), ''),
  })),
})

ministry.fetchSetLoading()
department.fetchSetLoading()
onMounted(() => {
  department.fetchPage()
  ministry.fetchPage(1, '', {
    params: {
      limit: 10000,
    },
  })
})

const formFields = createFormFields(() => [
  {
    type: INPUT_TYPES.SEARCH,
    class: 'lg:col-span-3',
    props: {
      name: 'q',
      placeholder: 'ค้นหากรม',
    },
  },
  {
    type: INPUT_TYPES.SELECT,
    props: {
      name: 'ministry_id',
      placeholder: 'เลือกกระทรวง',
      options: ArrayHelper.toOptions(ministry.fetch.items, 'id', 'name_th'),
      loading: ministry.fetch.status.isLoading,
      clearable: true,
    },
  },
])

watchDebounced(form.values, (values) => {
  department.fetchSearch(values.q || '', {
    params: {
      ministry_id: values.ministry_id || undefined,
    },
  })
}, {
  debounce: 300,
  deep: true,
})

const tableOptions = useTable<IDepartment>({
  repo: department,
  columns: () => [
    {
      accessorKey: 'name_th',
      header: 'ชื่อกรม (ภาษาไทย)',
      type: COLUMN_TYPES.TEXT,
      meta: {
        max: 40,
      },
    },
    {
      accessorKey: 'name_en',
      header: 'ชื่อกรม (ภาษาอังกฤษ)',
      type: COLUMN_TYPES.TEXT,
      meta: {
        max: 40,
      },
    },
    {
      header: 'จำนวนหน่วยงาน',
      accessorKey: 'divisions_count',
      type: COLUMN_TYPES.NUMBER,
      meta: {
        class: {
          th: 'text-right',
          td: 'text-right',
        },
      },
      cell: ({
        row,
      }) => {
        return getCount(row.original).totalDivisions
      },
    },
    {
      accessorKey: 'actions',
      header: '',
    },
  ],
})

const getCount = (values: IDepartment) => {
  const totalDivisions = values.division?.length || 0

  return {
    totalDivisions,
  }
}

const onEdit = (values: IDepartment) => {
  editModal.open({
    isEditing: true,
    values: values,
    status: () => department.update.status,
    onSubmit: (payload: IDepartment) => {
      department.updateRun(values.id, {
        data: payload,
      })
    },
  })
}

const onAdd = () => {
  addModal.open({
    status: () => department.add.status,
    onSubmit: (payload: IDepartment) => {
      department.addRun({
        data: payload,
      })
    },
  })
}

useWatchTrue(
  () => department.update.status.isSuccess,
  () => {
    editModal.close()
    department.fetchPage()

    noti.success({
      title: 'แก้ไขกรมสำเร็จ',
      description: 'คุณได้แก้ไขกรมเรียบร้อยแล้ว',
    })
  },
)

useWatchTrue(
  () => department.update.status.isError,
  () => {
    noti.error({
      title: 'แก้ไขกรมไม่สำเร็จ',
      description: 'เกิดข้อผิดพลาดในการแก้ไขกรม กรุณาลองใหม่อีกครั้ง',
    })
  },
)

useWatchTrue(
  () => department.delete.status.isSuccess,
  () => {
    noti.success({
      title: 'ลบกรมสำเร็จ',
      description: 'คุณได้ลบกรมเรียบร้อยแล้ว',
    })
  },
)

useWatchTrue(
  () => department.delete.status.isError,
  () => {
    noti.error({
      title: 'ลบกรมไม่สำเร็จ',
      description: 'เกิดข้อผิดพลาดในการลบกรม กรุณาลองใหม่อีกครั้ง',
    })
  },
)

useWatchTrue(
  () => department.add.status.isSuccess,
  () => {
    addModal.close()
    department.fetchPage()

    noti.success({
      title: 'เพิ่มกรมสำเร็จ',
      description: 'คุณได้เพิ่มกรมเรียบร้อยแล้ว',
    })

    dialog.close()
  },
)

useWatchTrue(
  () => department.add.status.isError,
  () => {
    noti.error({
      title: 'เพิ่มกรมไม่สำเร็จ',
      description: 'เกิดข้อผิดพลาดในการเพิ่มกรม กรุณาลองใหม่อีกครั้ง',
    })

    dialog.close()
  },
)

const onDelete = (values: IDepartment) => {
  dialog.confirm({
    title: 'ยืนยันการลบ',
    description: `คุณต้องการลบกรม "${values.name_th}" หรือไม่?`,
    confirmText: 'ยืนยัน',
    cancelText: 'ยกเลิก',
  }).then(() => {
    department.deleteRun(values.id)

    dialog.loading({
      title: 'กรุณารอสักครู่...',
      description: 'กำลังส่งข้อมูล...',
    })
  })
}
</script>
