model project_usages {
    id          String  @id @default(uuid()) @db.Uuid
    project_id  String  @db.Uuid
    amount      Float
    created_at  DateTime @default(now())
    cycle_id    String  @db.Uuid

    project projects @relation(fields: [project_id], references: [id], onDelete: Cascade)
    cycle   cycles   @relation(fields: [cycle_id], references: [id], onDelete: Cascade)

    @@index([project_id])
    @@index([cycle_id])
}
