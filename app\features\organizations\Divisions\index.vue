<template>
  <div>
    <TeleportSafe to="#page-header">
      <Button
        trailing-icon="basil:plus-solid"
        @click="onAdd"
      >
        เพิ่มหน่วยงาน
      </Button>
    </TeleportSafe>
    <FormFields
      :form="form"
      :options="formFields"
      class="
        mb-4 grid w-full gap-4
        lg:grid-cols-5
      "
    />
    <Table
      :options="tableOptions"
      @pageChange="devision.fetchPageChange"
      @search="devision.fetchSearch"
    >
      <template #actions-cell="{ row }">
        <div class="flex">
          <ButtonActionIcon
            icon="mage:pen"
            color="neutral"
            @click="onEdit(row.original)"
          />
          <ButtonActionIcon
            icon="prime:trash"
            color="error"
            @click="onDelete(row.original)"
          />
        </div>
      </template>
    </Table>
  </div>
</template>

<script lang="ts" setup>
import { watchDebounced } from '@vueuse/core'
import * as v from 'valibot'
import Form from './Form.vue'
import { COLUMN_TYPES } from '#core/components/Table/types'
import type { IDivision } from '~/types/organizations'
import { useOrgDepartmentPageLoader, useOrgDivisionPageLoader, useOrgMinistryPageLoader } from '~/loaders/organizations'
import { INPUT_TYPES } from '#core/components/Form/types'

const devision = useOrgDivisionPageLoader()
const department = useOrgDepartmentPageLoader()
const ministry = useOrgMinistryPageLoader()
const dialog = useDialog()
const noti = useNotification()
const overlay = useOverlay()
const editModal = overlay.create(Form)
const addModal = overlay.create(Form)

devision.fetchSetLoading()
department.fetchSetLoading()
ministry.fetchSetLoading()

onMounted(() => {
  devision.fetchPage()
  department.fetchPage(1, '', {
    params: {
      limit: 10000,
    },
  })

  ministry.fetchPage(1, '', {
    params: {
      limit: 10000,
    },
  })
})

const form = useForm({
  validationSchema: toTypedSchema(v.object({
    q: v.optional(v.pipe(v.string()), ''),
    ministry_id: v.optional(v.pipe(v.string()), ''),
    department_id: v.optional(v.pipe(v.string()), ''),
  })),
})

const formFields = createFormFields(() => [
  {
    type: INPUT_TYPES.SEARCH,
    class: 'lg:col-span-3',
    props: {
      name: 'q',
      placeholder: 'ค้นหากรม',
    },
  },
  {
    type: INPUT_TYPES.SELECT,
    props: {
      name: 'ministry_id',
      placeholder: 'เลือกกระทรวง',
      options: ArrayHelper.toOptions(ministry.fetch.items, 'id', 'name_th'),
      loading: ministry.fetch.status.isLoading,
      clearable: true,
    },
    on: {
      change: () => {
        form.setFieldValue('department_id', '')
      },
    },
  },
  {
    type: INPUT_TYPES.SELECT,
    props: {
      name: 'department_id',
      placeholder: 'เลือกกรม',
      options: ArrayHelper.toOptions(department.fetch.items.filter((v) => v.ministry_id === form.values.ministry_id), 'id', 'name_th'),
      loading: department.fetch.status.isLoading,
      clearable: true,
      disabled: !form.values.ministry_id,
    },
  },
])

watchDebounced(form.values, (values) => {
  devision.fetchSearch(values.q || '', {
    params: {
      ministry_id: values.ministry_id || undefined,
      department_id: values.department_id || undefined,
    },
  })
}, {
  debounce: 300,
  deep: true,
})

const tableOptions = useTable<IDivision>({
  repo: devision,
  columns: () => [
    {
      accessorKey: 'name_th',
      header: 'ชื่อหน่วยงาน (ภาษาไทย)',
      type: COLUMN_TYPES.TEXT,
      meta: {
        max: 40,
      },
    },
    {
      accessorKey: 'name_en',
      header: 'ชื่อหน่วยงาน (ภาษาอังกฤษ)',
      type: COLUMN_TYPES.TEXT,
      meta: {
        max: 40,
      },
    },
    {
      accessorKey: 'actions',
      header: '',
    },
  ],
})

const onEdit = (values: IDivision) => {
  editModal.open({
    isEditing: true,
    values: values,
    status: () => devision.update.status,
    onSubmit: (payload: IDivision) => {
      devision.updateRun(values.id, {
        data: payload,
      })
    },
  })
}

const onAdd = () => {
  addModal.open({
    status: () => devision.add.status,
    onSubmit: (payload: IDivision) => {
      devision.addRun({
        data: payload,
      })
    },
  })
}

useWatchTrue(
  () => devision.update.status.isSuccess,
  () => {
    editModal.close()
    devision.fetchPage()

    noti.success({
      title: 'แก้ไขหน่วยงานสำเร็จ',
      description: 'คุณได้แก้ไขหน่วยงานเรียบร้อยแล้ว',
    })
  },
)

useWatchTrue(
  () => devision.update.status.isError,
  () => {
    noti.error({
      title: 'แก้ไขหน่วยงานไม่สำเร็จ',
      description: 'เกิดข้อผิดพลาดในการแก้ไขหน่วยงาน กรุณาลองใหม่อีกครั้ง',
    })
  },
)

useWatchTrue(
  () => devision.delete.status.isSuccess,
  () => {
    noti.success({
      title: 'ลบหน่วยงานสำเร็จ',
      description: 'คุณได้ลบหน่วยงานเรียบร้อยแล้ว',
    })
  },
)

useWatchTrue(
  () => devision.delete.status.isError,
  () => {
    noti.error({
      title: 'ลบหน่วยงานไม่สำเร็จ',
      description: 'เกิดข้อผิดพลาดในการลบหน่วยงาน กรุณาลองใหม่อีกครั้ง',
    })
  },
)

useWatchTrue(
  () => devision.add.status.isSuccess,
  () => {
    addModal.close()
    devision.fetchPage()

    noti.success({
      title: 'เพิ่มหน่วยงานสำเร็จ',
      description: 'คุณได้เพิ่มหน่วยงานเรียบร้อยแล้ว',
    })

    dialog.close()
  },
)

useWatchTrue(
  () => devision.add.status.isError,
  () => {
    noti.error({
      title: 'เพิ่มหน่วยงานไม่สำเร็จ',
      description: 'เกิดข้อผิดพลาดในการเพิ่มหน่วยงาน กรุณาลองใหม่อีกครั้ง',
    })

    dialog.close()
  },
)

const onDelete = (values: IDivision) => {
  dialog.confirm({
    title: 'ยืนยันการลบ',
    description: `คุณต้องการลบหน่วยงาน "${values.name_th}" หรือไม่?`,
    confirmText: 'ยืนยัน',
    cancelText: 'ยกเลิก',
  }).then(() => {
    devision.deleteRun(values.id)
    dialog.loading({
      title: 'กรุณารอสักครู่...',
      description: 'กำลังส่งข้อมูล...',
    })
  })
}
</script>
