{"name": "csp-frontend", "private": true, "type": "module", "scripts": {"build": "nuxt build", "dev": "nuxt dev -o", "generate": "nuxt generate", "preview": "nuxt preview", "postinstall": "nuxt prepare", "lint": "eslint . --quiet", "lint:fix": "eslint --fix . --quiet", "prepare": "husky", "test": "vitest", "test:run": "vitest run", "test:coverage": "vitest --coverage"}, "dependencies": {"@finema/core": "^2.26.8", "@nuxt/eslint": "1.7.1", "@nuxt/test-utils": "^3.19.2", "chart.js": "^4.4.9", "eslint": "^9.0.0", "nuxt": "^4.0.1", "vue": "^3.5.18", "vue-chartjs": "^5.3.2", "vue-router": "^4.5.1"}, "devDependencies": {"@vue/test-utils": "^2.4.6", "eslint-plugin-better-tailwindcss": "^3.4.1", "eslint-plugin-unused-imports": "^4.1.4", "happy-dom": "^18.0.1", "husky": "^9.1.7", "lint-staged": "^16.1.2", "playwright-core": "^1.54.1", "vitest": "^3.2.4"}, "lint-staged": {"*": "eslint --fix . --quiet"}}