import type { NavigationMenuItem } from '@nuxt/ui'

export const routes = {
  home: {
    label: 'แดชบอร์ด',
    to: '/',
    icon: 'material-symbols:dashboard-outline',
  },
  user: {
    users: {
      label: 'ผู้ใช้งานทั้งหมด',
      to: '/users',
      icon: 'humbleicons:users',
    },
    userCreate: {
      label: 'สร้างผู้ใช้งาน',
      to: '/users/create',
    },
    userById: (id: string, label = '') => ({
      label: label || 'ข้อมูลผู้ใช้งาน',
      to: `/users/${id}`,
    }),
  },
  changePassword: {
    label: 'เปลี่ยนรหัสผ่าน',
    to: '/change-password',
    icon: 'solar:lock-password-bold',
  },
  project: {
    projectById: (id: string, label = '') => ({
      label: label || 'ข้อมูลโครงการ',
      to: `/projects/${id}`,
    }),
    projects: {
      label: 'โครงการทั้งหมด',
      to: '/projects',
      icon: 'ix:project',
    },
    projectCreate: {
      label: 'สร้างโครงการ',
      to: '/projects/create',
    },
  },
  login: {
    label: 'Login',
    to: '/login',
  },
  logout: {
    label: 'Logout',
    to: '/api/auth/logout',
  },
  organization: {
    ministries: {
      label: 'กระทรวง',
      to: '/organizations/ministries',
      icon: 'ix:building1',
    },
    departments: {
      label: 'กรม',
      to: '/organizations/departments',
      icon: 'ix:pc-tower',
    },
    divisions: {
      label: 'หน่วยงาน',
      to: '/organizations/divisions',
      icon: 'tdesign:houses-1',
    },
  },

} as const

export const sidebarUser: NavigationMenuItem[] = [
  routes.home,
  routes.project.projects,
]

export const sidebarAdmin: NavigationMenuItem[] = [
  routes.user.users,
  routes.organization.ministries,
  routes.organization.departments,
  routes.organization.divisions,
]
